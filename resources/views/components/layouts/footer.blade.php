    <!-- Footer -->
    <footer class="footer bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-3 mb-4 mb-lg-0">
                    <h5 class="mb-4">EndpointSync</h5>
                    <p>Empowering small businesses with smart automation solutions that save time, reduce costs, and improve efficiency.</p>
                    <div class="social-icons mt-3">
                        @php
                            $facebook = \App\Models\Setting::get('social_facebook');
                            $twitter = \App\Models\Setting::get('social_twitter');
                            $linkedin = \App\Models\Setting::get('social_linkedin');
                            $instagram = \App\Models\Setting::get('social_instagram');
                        @endphp

                        @if($facebook)
                            <a href="{{ $facebook }}" class="text-white me-3" target="_blank" rel="noopener"><i class="fab fa-facebook-f"></i></a>
                        @endif

                        @if($twitter)
                            <a href="{{ $twitter }}" class="text-white me-3" target="_blank" rel="noopener"><i class="fab fa-twitter"></i></a>
                        @endif

                        @if($linkedin)
                            <a href="{{ $linkedin }}" class="text-white me-3" target="_blank" rel="noopener"><i class="fab fa-linkedin-in"></i></a>
                        @endif

                        @if($instagram)
                            <a href="{{ $instagram }}" class="text-white" target="_blank" rel="noopener"><i class="fab fa-instagram"></i></a>
                        @endif
                    </div>
                </div>
                <div class="col-lg-3 mb-4 mb-lg-0">
                    <h5 class="mb-4">Quick Links</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="{{ route('home') }}#hero" class="text-white text-decoration-none">Home</a></li>
                        <li class="mb-2"><a href="{{ route('home') }}#challenges" class="text-white text-decoration-none">Challenges</a></li>
                        <li class="mb-2"><a href="{{ route('home') }}#process" class="text-white text-decoration-none">Our Approach</a></li>
                        <li class="mb-2"><a href="{{ route('home') }}#benefits" class="text-white text-decoration-none">Benefits</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 mb-4 mb-lg-0">
                    <h5 class="mb-4">Resources</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="{{ route('privacy-policy') }}" class="text-white text-decoration-none">Privacy Policy</a></li>
                        <li class="mb-2"><a href="{{ route('terms-of-service') }}" class="text-white text-decoration-none">Terms of Service</a></li>
                        <li class="mb-2"><a href="{{ route('home') }}#about" class="text-white text-decoration-none">About Us</a></li>
                        <li class="mb-2"><a href="{{ route('home') }}#faq" class="text-white text-decoration-none">FAQs</a></li>
                    </ul>
                </div>
                <div class="col-lg-3">
                    <h5 class="mb-4">Contact Us</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                        <li class="mb-2"><i class="fas fa-phone me-2"></i> ******.283.4386</li>
                        <li class="mb-2">
                            <i class="fas fa-map-marker-alt me-2 align-top"></i>
                            <span>
                                1771 Robson Street<br>
                                Vancouver, BC<br>
                                V6G 3B7<br>
                                Canada
                            </span>
                        </li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="row">
                <div class="col-12 text-center">
                    <p class="mb-0">&copy; {{ date('Y') }} EndpointSync. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Consultation Modal -->
    <div class="modal fade" id="consultationModal" tabindex="-1" aria-labelledby="consultationModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content border-0 shadow-lg">
                <div class="modal-header bg-gradient-primary text-white position-relative py-4">
                    <div class="modal-title-wrapper w-100 text-center">
                        <h5 class="modal-title fw-bold" id="consultationModalLabel">
                            <i class="fas fa-calendar-check me-2"></i>Book Your Free Consultation
                        </h5>
                        <p class="mb-0 mt-1 small">Let us help you automate your business processes</p>
                    </div>
                    <button type="button" class="btn-close btn-close-white position-absolute end-0 me-3" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="consultation-form-intro text-center mb-4">
                        <p class="lead mb-0">Tell us about your business needs and we'll show you how automation can help</p>
                    </div>
                    <form id="consultationForm" class="needs-validation consultation-form" novalidate>
                        @csrf
                        <div class="row g-4">
                            <!-- Name Field -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">
                                    <i class="fas fa-user text-primary me-2"></i>Your Name <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <input type="text" class="form-control form-control-lg border-end-0" id="name" name="name" placeholder="John Smith" required>
                                    <span class="input-group-text bg-white border-start-0">
                                        <i class="fas fa-check-circle text-success d-none"></i>
                                    </span>
                                </div>
                                <div class="invalid-feedback">
                                    Please provide your name.
                                </div>
                            </div>

                            <!-- Email Field -->
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope text-primary me-2"></i>Email Address <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <input type="email" class="form-control form-control-lg border-end-0" id="email" name="email" placeholder="<EMAIL>" required>
                                    <span class="input-group-text bg-white border-start-0">
                                        <i class="fas fa-check-circle text-success d-none"></i>
                                    </span>
                                </div>
                                <div class="invalid-feedback">
                                    Please provide a valid email address.
                                </div>
                            </div>

                            <!-- Website URL Field -->
                            <div class="col-12 mb-3">
                                <label for="website_url" class="form-label">
                                    <i class="fas fa-globe text-primary me-2"></i>Website URL <span class="text-muted">(optional)</span>
                                </label>
                                <input type="url" class="form-control form-control-lg" id="website_url" name="website_url" placeholder="https://example.com">
                                <div class="invalid-feedback">
                                    Please provide a valid URL.
                                </div>
                            </div>

                            <!-- Business Description Field -->
                            <div class="col-12 mb-4">
                                <label for="business_description" class="form-label">
                                    <i class="fas fa-briefcase text-primary me-2"></i>Business Description <span class="text-danger">*</span>
                                </label>
                                <p class="form-text text-muted mb-2">Adding as much detail as you can will really help us understand your business.</p>
                                <textarea class="form-control form-control-lg" id="business_description" name="business_description" rows="3" placeholder="Tell us about your business, industry, size, and goals..." required></textarea>
                                <div class="invalid-feedback">
                                    Please describe your business.
                                </div>
                            </div>

                            <!-- Pain Point Field -->
                            <div class="col-12 mb-4">
                                <label for="pain_point" class="form-label">
                                    <i class="fas fa-exclamation-circle text-primary me-2"></i>Business Pain Point <span class="text-danger">*</span>
                                </label>
                                <p class="form-text text-muted mb-2">What operational challenge would you like addressed?</p>
                                <textarea class="form-control form-control-lg" id="pain_point" name="pain_point" rows="3" placeholder="Describe the specific operational challenge you're facing..." required></textarea>
                                <div class="invalid-feedback">
                                    Please describe your main operational pain point.
                                </div>
                            </div>

                            <!-- Current Automation Field -->
                            <div class="col-12 mb-4">
                                <label for="current_automation" class="form-label">
                                    <i class="fas fa-robot text-primary me-2"></i>Current Automation <span class="text-danger">*</span>
                                </label>
                                <p class="form-text text-muted mb-2">Are you using any automation tools or software currently?</p>
                                <textarea class="form-control form-control-lg" id="current_automation" name="current_automation" rows="3" placeholder="List any automation tools or software you're currently using..." required></textarea>
                                <div class="invalid-feedback">
                                    Please list any current automation tools you're using.
                                </div>
                            </div>

                            <!-- Additional Notes Field -->
                            <div class="col-12 mb-4">
                                <label for="additional_notes" class="form-label">
                                    <i class="fas fa-sticky-note text-primary me-2"></i>Additional Notes <span class="text-muted">(optional)</span>
                                </label>
                                <textarea class="form-control form-control-lg" id="additional_notes" name="additional_notes" rows="3" placeholder="Any other information you'd like to share..."></textarea>
                            </div>
                        </div>

                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-primary btn-lg rounded-pill py-3 fw-bold">
                                <i class="fas fa-paper-plane me-2"></i>Submit Your Request
                            </button>
                            <p class="text-center text-muted mt-2 small">
                                <i class="fas fa-lock me-1"></i>Your information is secure and will not be shared with third parties.
                            </p>
                        </div>
                    </form>

                    <!-- Success Message (Hidden by default) -->
                    <div id="successMessage" class="text-center py-5 d-none">
                        <div class="success-animation mb-4">
                            <i class="fas fa-check-circle text-success fa-5x mb-4 animate__animated animate__bounceIn"></i>
                        </div>
                        <h3 class="h3 mb-3 fw-bold">Thank You!</h3>
                        <p class="lead mb-4">Your consultation request has been received. One of our team members will contact you shortly to schedule your session.</p>
                        <button type="button" class="btn btn-primary btn-lg rounded-pill px-5 py-3" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- AOS Animation JS -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />

    <!-- Parallax JS -->
    <script src="https://cdn.jsdelivr.net/npm/simple-parallax-js@5.6.2/dist/simpleParallax.min.js"></script>

    <!-- Custom JS -->
    <script src="{{ asset('js/main.js') }}"></script>

    <!-- Initialize AOS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true
            });
        });
    </script>

    @yield('additional_scripts')

    <!-- Schema.org Markup -->
    @include('components.schema-markup')
</body>
</html>
