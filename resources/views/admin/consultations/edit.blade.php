<x-layouts.admin :title="__('Edit Consultation')">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">{{ __('Edit Consultation') }}</h1>
            <p class="text-muted small">{{ __('Update consultation information') }}</p>
        </div>
        <a href="{{ route('admin.consultations.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            {{ __('Back to List') }}
        </a>
    </div>

    <div class="card border-0 shadow-sm">
        <form action="{{ route('admin.consultations.update', $consultation) }}" method="POST">
            @csrf
            @method('PUT')

            <div class="card-body p-4">
                <div class="row">
                    <div class="col-md-6">
                        <h3 class="h5 mb-3">{{ __('Contact Information') }}</h3>
                        <div class="mb-3">
                            <label for="name" class="form-label">{{ __('Name') }} <span class="text-danger">*</span></label>
                            <input type="text" name="name" id="name" value="{{ old('name', $consultation->name) }}" class="form-control @error('name') is-invalid @enderror" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">{{ __('Email') }} <span class="text-danger">*</span></label>
                            <input type="email" name="email" id="email" value="{{ old('email', $consultation->email) }}" class="form-control @error('email') is-invalid @enderror" required>
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="website_url" class="form-label">{{ __('Website URL') }}</label>
                            <input type="url" name="website_url" id="website_url" value="{{ old('website_url', $consultation->website_url) }}" class="form-control @error('website_url') is-invalid @enderror">
                            @error('website_url')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h3 class="h5 mb-3">{{ __('Status Information') }}</h3>
                        <div class="mb-3">
                            <label for="status_id" class="form-label">{{ __('Status') }} <span class="text-danger">*</span></label>
                            <select name="status_id" id="status_id" class="form-select @error('status_id') is-invalid @enderror" required>
                                @foreach($statuses as $status)
                                    <option value="{{ $status->id }}"
                                        {{ old('status_id', $consultation->status_id) == $status->id ? 'selected' : '' }}
                                        data-color="{{ $status->color }}">
                                        {{ $status->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('status_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <div id="status-preview" class="alert border" style="background-color: {{ old('status_id', $consultation->status) ? $consultation->status->color : '#6c757d' }}; color: white;">
                                <strong>{{ __('Status Preview') }}</strong>
                                <div id="status-description" class="small mt-1">
                                    @if($consultation->status && $consultation->status->description)
                                        {{ $consultation->status->description }}
                                    @else
                                        {{ __('No description available') }}
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <p class="text-muted small">{{ __('Submitted on') }}: {{ $consultation->created_at->format('F j, Y \a\t g:i A') }}</p>
                        </div>
                    </div>
                </div>

                <hr class="my-4">

                <h3 class="h5 mb-3">{{ __('Business Information') }}</h3>
                <div class="mb-3">
                    <label for="business_description" class="form-label">{{ __('Business Description') }} <span class="text-danger">*</span></label>
                    <textarea name="business_description" id="business_description" rows="4" class="form-control @error('business_description') is-invalid @enderror" required>{{ old('business_description', $consultation->business_description) }}</textarea>
                    @error('business_description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-3">
                    <label for="pain_point" class="form-label">{{ __('Pain Points') }} <span class="text-danger">*</span></label>
                    <textarea name="pain_point" id="pain_point" rows="4" class="form-control @error('pain_point') is-invalid @enderror" required>{{ old('pain_point', $consultation->pain_point) }}</textarea>
                    @error('pain_point')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-3">
                    <label for="current_automation" class="form-label">{{ __('Current Automation') }} <span class="text-danger">*</span></label>
                    <textarea name="current_automation" id="current_automation" rows="4" class="form-control @error('current_automation') is-invalid @enderror" required>{{ old('current_automation', $consultation->current_automation) }}</textarea>
                    @error('current_automation')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mb-3">
                    <label for="additional_notes" class="form-label">{{ __('Additional Notes') }}</label>
                    <textarea name="additional_notes" id="additional_notes" rows="4" class="form-control @error('additional_notes') is-invalid @enderror">{{ old('additional_notes', $consultation->additional_notes) }}</textarea>
                    @error('additional_notes')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>
            <div class="card-footer bg-white d-flex justify-content-end py-3">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-check me-2"></i>
                    {{ __('Update Consultation') }}
                </button>
            </div>
        </form>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const statusSelect = document.getElementById('status_id');
            const statusPreview = document.getElementById('status-preview');
            const statusDescription = document.getElementById('status-description');

            // Store status descriptions
            const statusData = {
                @foreach($statuses as $status)
                    {{ $status->id }}: {
                        color: "{{ $status->color }}",
                        description: "{{ $status->description ?: __('No description available') }}"
                    },
                @endforeach
            };

            function updateStatusPreview() {
                const selectedStatusId = statusSelect.value;
                const statusInfo = statusData[selectedStatusId];

                if (statusInfo) {
                    statusPreview.style.backgroundColor = statusInfo.color;
                    statusDescription.textContent = statusInfo.description;
                }
            }

            statusSelect.addEventListener('change', updateStatusPreview);
        });
    </script>
</x-layouts.admin>
