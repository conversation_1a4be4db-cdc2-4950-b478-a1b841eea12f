<x-layouts.admin :title="__('Manage Consultations')">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 fw-bold mb-1">{{ __('Consultations') }}</h1>
            <p class="text-muted mb-0">{{ __('Manage consultation requests from potential clients') }}</p>
        </div>
        <a href="{{ route('admin.consultations.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            {{ __('New Consultation') }}
        </a>
    </div>

    <!-- Filters and Search -->
    <div class="card admin-card border-0 shadow-sm mb-4">
        <div class="card-body">
            <form action="{{ route('admin.consultations.index') }}" method="GET" class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label for="search" class="form-label">{{ __('Search') }}</label>
                    <div class="input-group">
                        <span class="input-group-text bg-white"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" id="search" name="search" placeholder="Search by name or email" value="{{ request('search') }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">{{ __('Status') }}</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">{{ __('All Statuses') }}</option>
                        @foreach($statuses as $status)
                            <option value="{{ $status->id }}" {{ request('status') == $status->id ? 'selected' : '' }}>
                                {{ $status->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="date" class="form-label">{{ __('Date Range') }}</label>
                    <select class="form-select" id="date" name="date">
                        <option value="">{{ __('All Time') }}</option>
                        <option value="today" {{ request('date') == 'today' ? 'selected' : '' }}>{{ __('Today') }}</option>
                        <option value="week" {{ request('date') == 'week' ? 'selected' : '' }}>{{ __('This Week') }}</option>
                        <option value="month" {{ request('date') == 'month' ? 'selected' : '' }}>{{ __('This Month') }}</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter me-2"></i>{{ __('Filter') }}
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="card admin-card border-0 shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title fw-bold mb-0">{{ __('All Consultations') }}</h5>
                <span class="badge bg-primary">{{ $consultations->total() }} {{ __('Total') }}</span>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>{{ __('Name') }}</th>
                            <th>{{ __('Email') }}</th>
                            <th>{{ __('Date') }}</th>
                            <th>{{ __('Status') }}</th>
                            <th class="text-end">{{ __('Actions') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse ($consultations as $consultation)
                            <tr>
                                <td class="fw-medium">{{ $consultation->name }}</td>
                                <td>{{ $consultation->email }}</td>
                                <td>{{ $consultation->created_at->format('M d, Y') }}</td>
                                <td>
                                    @if ($consultation->status)
                                        <span class="badge" style="background-color: {{ $consultation->status->color }};">
                                            {{ $consultation->status->name }}
                                        </span>
                                    @else
                                        <span class="badge bg-secondary">{{ __('No Status') }}</span>
                                    @endif
                                </td>
                                <td class="text-end">
                                    <div class="btn-group">
                                        <a href="{{ route('admin.consultations.show', $consultation) }}" class="btn btn-sm btn-outline-primary" data-bs-toggle="tooltip" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.consultations.edit', $consultation) }}" class="btn btn-sm btn-outline-secondary" data-bs-toggle="tooltip" title="Edit">
                                            <i class="fas fa-pencil-alt"></i>
                                        </a>
                                        <div class="dropdown d-inline">
                                            <button class="btn btn-sm btn-outline-success dropdown-toggle" type="button" id="statusDropdown{{ $consultation->id }}" data-bs-toggle="dropdown" aria-expanded="false" title="Update Status">
                                                <i class="fas fa-tag"></i>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="statusDropdown{{ $consultation->id }}">
                                                @foreach($statuses as $status)
                                                    @if(!$consultation->status || $consultation->status->id != $status->id)
                                                        <li>
                                                            <form action="{{ route('admin.consultations.update-status', $consultation) }}" method="POST">
                                                                @csrf
                                                                @method('PATCH')
                                                                <input type="hidden" name="status_id" value="{{ $status->id }}">
                                                                <button type="submit" class="dropdown-item">
                                                                    <span class="badge me-2" style="background-color: {{ $status->color }};">
                                                                        &nbsp;
                                                                    </span>
                                                                    {{ $status->name }}
                                                                </button>
                                                            </form>
                                                        </li>
                                                    @endif
                                                @endforeach
                                            </ul>
                                        </div>
                                        <form action="{{ route('admin.consultations.destroy', $consultation) }}" method="POST" class="d-inline" onsubmit="return confirm('{{ __('Are you sure you want to delete this consultation?') }}')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger" data-bs-toggle="tooltip" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5" class="text-center py-4">
                                    <div class="d-flex flex-column align-items-center py-5">
                                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                        <h5 class="fw-bold">{{ __('No consultations found') }}</h5>
                                        <p class="text-muted">{{ __('No consultation requests match your criteria') }}</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        <div class="card-footer bg-white py-3">
            {{ $consultations->links() }}
        </div>
    </div>
</x-layouts.admin>
