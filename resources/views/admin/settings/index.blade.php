<x-layout-admin :title="__('Site Settings')">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 fw-bold mb-1">{{ __('Site Settings') }}</h1>
            <p class="text-muted mb-0">{{ __('Manage your website settings') }}</p>
        </div>
        <a href="{{ route('admin.dashboard') }}" class="btn btn-outline-secondary btn-sm">
            <i class="fas fa-arrow-left me-1"></i> {{ __('Back to Dashboard') }}
        </a>
    </div>

    <div class="row g-4">
        <!-- Social Media Settings Card -->
        <div class="col-md-6 col-lg-4">
            <div class="card admin-card h-100 border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-4">
                        <div class="me-3 d-flex align-items-center justify-content-center rounded-circle bg-primary-soft" style="width: 56px; height: 56px;">
                            <i class="fas fa-share-alt text-primary fa-lg"></i>
                        </div>
                        <div>
                            <h5 class="card-title fw-bold mb-0">{{ __('Social Media') }}</h5>
                            <p class="card-text text-muted small mb-0">{{ __('Manage social media links') }}</p>
                        </div>
                    </div>
                    <p class="card-text">Configure your social media links that appear in the website footer.</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="social-preview">
                            @if(App\Models\Setting::get('social_facebook'))
                                <i class="fab fa-facebook-f text-primary me-2"></i>
                            @endif
                            @if(App\Models\Setting::get('social_twitter'))
                                <i class="fab fa-twitter text-info me-2"></i>
                            @endif
                            @if(App\Models\Setting::get('social_linkedin'))
                                <i class="fab fa-linkedin-in text-primary me-2"></i>
                            @endif
                            @if(App\Models\Setting::get('social_instagram'))
                                <i class="fab fa-instagram text-danger"></i>
                            @endif
                        </div>
                        <a href="{{ route('admin.settings.social-media') }}" class="btn btn-primary btn-sm">
                            {{ __('Manage') }}
                            <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Consultation Status Settings Card -->
        <div class="col-md-6 col-lg-4">
            <div class="card admin-card h-100 border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-4">
                        <div class="me-3 d-flex align-items-center justify-content-center rounded-circle bg-primary-soft" style="width: 56px; height: 56px;">
                            <i class="fas fa-tags text-primary fa-lg"></i>
                        </div>
                        <div>
                            <h5 class="card-title fw-bold mb-0">{{ __('Consultation Statuses') }}</h5>
                            <p class="card-text text-muted small mb-0">{{ __('Manage consultation status types') }}</p>
                        </div>
                    </div>
                    <p class="card-text">Configure custom status types for tracking consultation progress.</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="small fw-bold">
                            <span class="text-muted">{{ App\Models\ConsultationStatus::count() }}</span> status types
                        </div>
                        <div>

                            <a href="{{ route('admin.consultation-statuses.index') }}" class="btn btn-primary btn-sm">
                                {{ __('Manage') }}
                                <i class="fas fa-arrow-right ms-1"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- API Settings Card -->
        <div class="col-md-6 col-lg-4">
            <div class="card admin-card h-100 border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-4">
                        <div class="me-3 d-flex align-items-center justify-content-center rounded-circle bg-primary-soft" style="width: 56px; height: 56px;">
                            <i class="fas fa-key text-primary fa-lg"></i>
                        </div>
                        <div>
                            <h5 class="card-title fw-bold mb-0">{{ __('API Settings') }}</h5>
                            <p class="card-text text-muted small mb-0">{{ __('Manage API configuration') }}</p>
                        </div>
                    </div>
                    <p class="card-text">Configure API settings including allowed IPs and token expiration.</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="small fw-bold">
                            <span class="text-muted">{{ App\Models\Setting::get('allowed_api_ips') ? count(explode(',', App\Models\Setting::get('allowed_api_ips'))) : 0 }}</span> IPs allowed
                        </div>
                        <a href="{{ route('admin.settings.api') }}" class="btn btn-primary btn-sm">
                            {{ __('Manage') }}
                            <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- API Tokens Card -->
        <div class="col-md-6 col-lg-4">
            <div class="card admin-card h-100 border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-4">
                        <div class="me-3 d-flex align-items-center justify-content-center rounded-circle bg-primary-soft" style="width: 56px; height: 56px;">
                            <i class="fas fa-lock text-primary fa-lg"></i>
                        </div>
                        <div>
                            <h5 class="card-title fw-bold mb-0">{{ __('API Tokens') }}</h5>
                            <p class="card-text text-muted small mb-0">{{ __('Manage API access tokens') }}</p>
                        </div>
                    </div>
                    <p class="card-text">Create and manage API tokens for secure API access.</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="small fw-bold">
                            <span class="text-muted">{{ \Laravel\Sanctum\PersonalAccessToken::count() }}</span> active tokens
                        </div>
                        <a href="{{ route('admin.settings.api-tokens') }}" class="btn btn-primary btn-sm">
                            {{ __('Manage') }}
                            <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-layout-admin>
