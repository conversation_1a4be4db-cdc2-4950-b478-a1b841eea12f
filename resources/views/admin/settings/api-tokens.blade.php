<x-layout-admin :title="__('API Tokens')">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 fw-bold mb-1">{{ __('API Tokens') }}</h1>
            <p class="text-muted mb-0">{{ __('Manage API access tokens') }}</p>
        </div>
        <a href="{{ route('admin.settings.index') }}" class="btn btn-outline-secondary btn-sm">
            <i class="fas fa-arrow-left me-1"></i> {{ __('Back to Settings') }}
        </a>
    </div>

    @if(session('plain_text_token'))
        <div class="alert alert-success mb-4 shadow-sm border-0" role="alert">
            <h5 class="alert-heading fw-bold"><i class="fas fa-check-circle me-2"></i> {{ __('Token Created Successfully') }}</h5>
            <p>{{ __('Your new API token has been created. Please copy it now as it will not be shown again:') }}</p>
            <div class="input-group mb-2">
                <input type="text" class="form-control font-monospace" value="{{ session('plain_text_token') }}" id="tokenInput" readonly>
                <button class="btn btn-outline-secondary" type="button" onclick="copyToken()">
                    <i class="fas fa-copy"></i>
                </button>
            </div>
            <small class="text-muted">{{ __('Make sure to store this token securely.') }}</small>
        </div>
    @endif

    <div class="row">
        <div class="col-md-5">
            <div class="card admin-card border-0 shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="card-title fw-bold mb-0">{{ __('Create New Token') }}</h5>
                </div>
                <div class="card-body p-4">
                    <form action="{{ route('admin.settings.api-tokens.store') }}" method="POST">
                        @csrf

                        <div class="mb-3">
                            <label for="user_id" class="form-label fw-bold">{{ __('User') }}</label>
                            <select class="form-select @error('user_id') is-invalid @enderror" id="user_id" name="user_id" required>
                                <option value="">{{ __('Select User') }}</option>
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}" {{ old('user_id') == $user->id ? 'selected' : '' }}>
                                        {{ $user->name }} ({{ $user->email }})
                                    </option>
                                @endforeach
                            </select>
                            @error('user_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="token_name" class="form-label fw-bold">{{ __('Token Name') }}</label>
                            <input type="text" class="form-control @error('token_name') is-invalid @enderror" id="token_name" name="token_name" value="{{ old('token_name') }}" placeholder="e.g., Production API Token" required>
                            @error('token_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">{{ __('Give your token a descriptive name for easy identification.') }}</div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label fw-bold">{{ __('Token Abilities') }}</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="abilities[]" value="create" id="ability_create" {{ is_array(old('abilities')) && in_array('create', old('abilities')) ? 'checked' : '' }}>
                                <label class="form-check-label" for="ability_create">
                                    {{ __('Create') }}
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="abilities[]" value="read" id="ability_read" {{ is_array(old('abilities')) && in_array('read', old('abilities')) ? 'checked' : '' }}>
                                <label class="form-check-label" for="ability_read">
                                    {{ __('Read') }}
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="abilities[]" value="update" id="ability_update" {{ is_array(old('abilities')) && in_array('update', old('abilities')) ? 'checked' : '' }}>
                                <label class="form-check-label" for="ability_update">
                                    {{ __('Update') }}
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="abilities[]" value="delete" id="ability_delete" {{ is_array(old('abilities')) && in_array('delete', old('abilities')) ? 'checked' : '' }}>
                                <label class="form-check-label" for="ability_delete">
                                    {{ __('Delete') }}
                                </label>
                            </div>
                            <div class="form-text">{{ __('Leave unchecked to grant full access.') }}</div>
                        </div>

                        <div class="mb-3">
                            <label for="expiration" class="form-label fw-bold">{{ __('Expiration (Days)') }}</label>
                            <input type="number" class="form-control @error('expiration') is-invalid @enderror" id="expiration" name="expiration" value="{{ old('expiration', App\Models\Setting::get('api_token_expiration', 0)) }}" min="0">
                            @error('expiration')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">{{ __('Number of days before token expires. Use 0 for no expiration.') }}</div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus-circle me-2"></i> {{ __('Create Token') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-7">
            <div class="card admin-card border-0 shadow-sm">
                <div class="card-header bg-white py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title fw-bold mb-0">{{ __('Active Tokens') }}</h5>
                        <span class="badge bg-primary">{{ $tokens->count() }} {{ __('Total') }}</span>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>{{ __('Name') }}</th>
                                    <th>{{ __('User') }}</th>
                                    <th>{{ __('Created') }}</th>
                                    <th>{{ __('Last Used') }}</th>
                                    <th class="text-end">{{ __('Actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($tokens as $token)
                                    <tr>
                                        <td class="fw-medium">
                                            {{ $token->name }}
                                            @if($token->expires_at)
                                                @if($token->expires_at->isPast())
                                                    <span class="badge bg-danger ms-1">{{ __('Expired') }}</span>
                                                @else
                                                    <span class="badge bg-warning text-dark ms-1">{{ __('Expires') }} {{ $token->expires_at->diffForHumans() }}</span>
                                                @endif
                                            @endif
                                        </td>
                                        <td>{{ $token->tokenable->name }}</td>
                                        <td>{{ $token->created_at->format('M d, Y') }}</td>
                                        <td>{{ $token->last_used_at ? $token->last_used_at->format('M d, Y H:i') : __('Never') }}</td>
                                        <td class="text-end">
                                            <form action="{{ route('admin.settings.api-tokens.destroy', $token->id) }}" method="POST" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('{{ __('Are you sure you want to revoke this token?') }}')">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="5" class="text-center py-4">
                                            <div class="d-flex flex-column align-items-center">
                                                <i class="fas fa-key text-muted mb-2" style="font-size: 2rem;"></i>
                                                <p class="text-muted mb-0">{{ __('No API tokens found.') }}</p>
                                                <p class="text-muted small">{{ __('Create a new token to get started.') }}</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function copyToken() {
            var tokenInput = document.getElementById('tokenInput');
            tokenInput.select();
            document.execCommand('copy');
            alert('Token copied to clipboard!');
        }
    </script>
</x-layout-admin>
