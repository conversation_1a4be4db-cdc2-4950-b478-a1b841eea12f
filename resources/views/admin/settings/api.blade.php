<x-layout-admin :title="__('API Settings')">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 fw-bold mb-1">{{ __('API Settings') }}</h1>
            <p class="text-muted mb-0">{{ __('Configure API access and security') }}</p>
        </div>
        <a href="{{ route('admin.settings.index') }}" class="btn btn-outline-secondary btn-sm">
            <i class="fas fa-arrow-left me-1"></i> {{ __('Back to Settings') }}
        </a>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card admin-card border-0 shadow-sm">
                <div class="card-body p-4">
                    <form action="{{ route('admin.settings.api.update') }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row mb-4">
                            <div class="col-12 mb-3">
                                <div class="alert alert-info d-flex align-items-center" role="alert">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <div>
                                        {{ __('Configure API settings to control access and security for your API endpoints.') }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row g-4">
                            @foreach($apiSettings as $setting)
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ $setting->key }}" class="form-label fw-bold">
                                            @if($setting->key == 'allowed_api_ips')
                                                <i class="fas fa-shield-alt me-2 text-primary"></i>
                                            @elseif($setting->key == 'api_token_expiration')
                                                <i class="fas fa-clock me-2 text-primary"></i>
                                            @elseif($setting->key == 'api_rate_limit')
                                                <i class="fas fa-tachometer-alt me-2 text-primary"></i>
                                            @endif
                                            {{ $setting->label }}
                                        </label>
                                        <div class="input-group">
                                            @if($setting->type == 'number')
                                                <input
                                                    type="number"
                                                    class="form-control @error($setting->key) is-invalid @enderror"
                                                    id="{{ $setting->key }}"
                                                    name="{{ $setting->key }}"
                                                    value="{{ old($setting->key, $setting->value) }}"
                                                    min="0"
                                                >
                                            @else
                                                <input
                                                    type="text"
                                                    class="form-control @error($setting->key) is-invalid @enderror"
                                                    id="{{ $setting->key }}"
                                                    name="{{ $setting->key }}"
                                                    value="{{ old($setting->key, $setting->value) }}"
                                                    placeholder="{{ $setting->description }}"
                                                >
                                            @endif
                                        </div>
                                        @error($setting->key)
                                            <div class="invalid-feedback d-block">
                                                {{ $message }}
                                            </div>
                                        @enderror
                                        <div class="form-text">{{ $setting->description }}</div>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <div class="d-flex justify-content-end mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i> {{ __('Save Changes') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card admin-card border-0 shadow-sm">
                <div class="card-header bg-white py-3">
                    <h5 class="card-title fw-bold mb-0">{{ __('API Documentation') }}</h5>
                </div>
                <div class="card-body p-4">
                    <div class="alert alert-primary d-flex align-items-center mb-4" role="alert">
                        <i class="fas fa-book me-2"></i>
                        <div>
                            {{ __('API documentation is available to help you integrate with our API endpoints.') }}
                        </div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="fw-bold mb-1">{{ __('API Documentation') }}</h6>
                            <p class="text-muted mb-0">{{ __('View the API documentation to learn how to use the API endpoints.') }}</p>
                        </div>
                        <div>
                            <a href="{{ route('admin.settings.api.documentation') }}" class="btn btn-outline-primary me-2">
                                <i class="fas fa-book me-1"></i> {{ __('View Documentation') }}
                            </a>
                            <a href="{{ route('admin.settings.api-tokens') }}" class="btn btn-outline-primary">
                                <i class="fas fa-key me-1"></i> {{ __('Manage API Tokens') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-layout-admin>
