<x-layout-admin :title="__('Social Media Settings')">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 fw-bold mb-1">{{ __('Social Media Settings') }}</h1>
            <p class="text-muted mb-0">{{ __('Manage your social media links') }}</p>
        </div>
        <a href="{{ route('admin.dashboard') }}" class="btn btn-outline-secondary btn-sm">
            <i class="fas fa-arrow-left me-1"></i> {{ __('Back to Dashboard') }}
        </a>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card admin-card border-0 shadow-sm">
                <div class="card-body p-4">
                    <form action="{{ route('admin.settings.social-media.update') }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row mb-4">
                            <div class="col-12 mb-3">
                                <div class="alert alert-info d-flex align-items-center" role="alert">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <div>
                                        {{ __('Enter your social media URLs below. Leave a field empty to hide that social media icon on the website.') }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row g-4">
                            @foreach($socialMediaSettings as $setting)
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ $setting->key }}" class="form-label">
                                            @if($setting->key == 'social_facebook')
                                                <i class="fab fa-facebook-f me-2 text-primary"></i>
                                            @elseif($setting->key == 'social_twitter')
                                                <i class="fab fa-twitter me-2 text-info"></i>
                                            @elseif($setting->key == 'social_linkedin')
                                                <i class="fab fa-linkedin-in me-2 text-primary"></i>
                                            @elseif($setting->key == 'social_instagram')
                                                <i class="fab fa-instagram me-2 text-danger"></i>
                                            @endif
                                            {{ $setting->label }}
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-light">
                                                <i class="fas fa-link"></i>
                                            </span>
                                            <input 
                                                type="url" 
                                                class="form-control @error($setting->key) is-invalid @enderror" 
                                                id="{{ $setting->key }}" 
                                                name="{{ $setting->key }}" 
                                                value="{{ old($setting->key, $setting->value) }}" 
                                                placeholder="{{ $setting->description }}"
                                            >
                                        </div>
                                        @error($setting->key)
                                            <div class="invalid-feedback d-block">
                                                {{ $message }}
                                            </div>
                                        @enderror
                                        <div class="form-text">{{ $setting->description }}</div>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <div class="d-flex justify-content-end mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i> {{ __('Save Changes') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-layout-admin>
