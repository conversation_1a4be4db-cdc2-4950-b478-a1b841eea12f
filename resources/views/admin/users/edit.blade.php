<x-layouts.admin :title="__('Edit User')">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">{{ __('Edit User') }}</h1>
            <p class="text-muted small">{{ __('Update user information') }}</p>
        </div>
        <a href="{{ route('admin.users.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            {{ __('Back to List') }}
        </a>
    </div>

    <div class="card border-0 shadow-sm">
        <form action="{{ route('admin.users.update', $user) }}" method="POST">
            @csrf
            @method('PUT')

            <div class="card-body p-4">
                <div class="d-flex align-items-center mb-4">
                    <div class="rounded-circle bg-primary-soft d-flex align-items-center justify-content-center me-3" style="width: 64px; height: 64px;">
                        <span class="text-primary fw-bold fs-4">{{ $user->initials() }}</span>
                    </div>
                    <div>
                        <h2 class="h4 fw-bold mb-1">{{ $user->name }}</h2>
                        <p class="text-muted small mb-0">{{ __('Member since') }} {{ $user->created_at->format('M d, Y') }}</p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h3 class="h5 mb-3">{{ __('Account Information') }}</h3>
                        <div class="mb-3">
                            <label for="name" class="form-label">{{ __('Name') }} <span class="text-danger">*</span></label>
                            <input type="text" name="name" id="name" value="{{ old('name', $user->name) }}" class="form-control @error('name') is-invalid @enderror" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">{{ __('Email') }} <span class="text-danger">*</span></label>
                            <input type="email" name="email" id="email" value="{{ old('email', $user->email) }}" class="form-control @error('email') is-invalid @enderror" required>
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">{{ __('Password') }}</label>
                            <input type="password" name="password" id="password" class="form-control @error('password') is-invalid @enderror">
                            <div class="form-text">{{ __('Leave blank to keep current password') }}</div>
                            @error('password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="mb-3">
                            <label for="password_confirmation" class="form-label">{{ __('Confirm Password') }}</label>
                            <input type="password" name="password_confirmation" id="password_confirmation" class="form-control @error('password_confirmation') is-invalid @enderror">
                            @error('password_confirmation')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h3 class="h5 mb-3">{{ __('Role & Permissions') }}</h3>
                        <div class="mb-3 form-check">
                            <input type="hidden" name="is_admin" value="0">
                            <input type="checkbox" name="is_admin" id="is_admin" value="1" {{ old('is_admin', $user->is_admin) ? 'checked' : '' }} class="form-check-input @error('is_admin') is-invalid @enderror" {{ $user->id === auth()->id() && $user->is_admin ? 'disabled' : '' }}>
                            <label for="is_admin" class="form-check-label">{{ __('Admin User') }}</label>
                            @if ($user->id === auth()->id() && $user->is_admin)
                                <input type="hidden" name="is_admin" value="1">
                                <div class="form-text">{{ __('You cannot remove admin privileges from your own account') }}</div>
                            @endif
                            @error('is_admin')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-white d-flex justify-content-end py-3">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-check me-2"></i>
                    {{ __('Update User') }}
                </button>
            </div>
        </form>
    </div>
</x-layouts.admin>
