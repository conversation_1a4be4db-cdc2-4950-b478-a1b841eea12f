<x-layout-admin :title="__('Admin Dashboard')">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 fw-bold mb-1">{{ __('Admin Dashboard') }}</h1>
            <p class="text-muted mb-0">{{ __('Manage your website content and users') }}</p>
        </div>
    </div>

    <div class="row g-4">
        <!-- Consultations Card -->
        <div class="col-md-6 col-lg-4">
            <div class="card admin-card h-100 border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-4">
                        <div class="me-3 d-flex align-items-center justify-content-center rounded-circle bg-primary-soft" style="width: 56px; height: 56px;">
                            <i class="fas fa-comments text-primary fa-lg"></i>
                        </div>
                        <div>
                            <h5 class="card-title fw-bold mb-0">{{ __('Consultations') }}</h5>
                            <p class="card-text text-muted small mb-0">{{ __('Manage consultation requests') }}</p>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="small fw-bold">
                            <span class="text-primary">{{ App\Models\Consultation::count() }}</span> total
                        </div>
                        <a href="{{ route('admin.consultations.index') }}" class="btn btn-primary btn-sm" wire:navigate>
                            {{ __('View All') }}
                            <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Users Card -->
        <div class="col-md-6 col-lg-4">
            <div class="card admin-card h-100 border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-4">
                        <div class="me-3 d-flex align-items-center justify-content-center rounded-circle bg-primary-soft" style="width: 56px; height: 56px;">
                            <i class="fas fa-users text-primary fa-lg"></i>
                        </div>
                        <div>
                            <h5 class="card-title fw-bold mb-0">{{ __('Users') }}</h5>
                            <p class="card-text text-muted small mb-0">{{ __('Manage user accounts') }}</p>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="small fw-bold">
                            <span class="text-primary">{{ App\Models\User::count() }}</span> total
                        </div>
                        <a href="{{ route('admin.users.index') }}" class="btn btn-primary btn-sm" wire:navigate>
                            {{ __('View All') }}
                            <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Card -->
        <div class="col-md-6 col-lg-4">
            <div class="card admin-card h-100 border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-4">
                        <div class="me-3 d-flex align-items-center justify-content-center rounded-circle bg-primary-soft" style="width: 56px; height: 56px;">
                            <i class="fas fa-cog text-primary fa-lg"></i>
                        </div>
                        <div>
                            <h5 class="card-title fw-bold mb-0">{{ __('Settings') }}</h5>
                            <p class="card-text text-muted small mb-0">{{ __('Manage website configuration') }}</p>
                        </div>
                    </div>
                    <div class="d-flex flex-column">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div class="small fw-bold">
                                <span class="text-muted">Account Settings</span>
                            </div>
                            <a href="{{ route('settings.profile') }}" class="btn btn-outline-primary btn-sm" wire:navigate>
                                <i class="fas fa-user-cog me-1"></i> {{ __('Profile') }}
                            </a>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="small fw-bold">
                                <span class="text-muted">Site Settings</span>
                            </div>
                            <a href="{{ route('admin.settings.index') }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-sliders-h me-1"></i> {{ __('Configure') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity Section -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card admin-card border-0 shadow-sm">
                <div class="card-header bg-white py-3">
                    <h5 class="card-title fw-bold mb-0">{{ __('Recent Activity') }}</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>{{ __('Type') }}</th>
                                    <th>{{ __('Description') }}</th>
                                    <th>{{ __('Date') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                    $recentConsultations = App\Models\Consultation::latest()->take(3)->get();
                                @endphp

                                @forelse($recentConsultations as $consultation)
                                    <tr>
                                        <td>
                                            <span class="badge bg-primary-soft text-primary">Consultation</span>
                                        </td>
                                        <td>New consultation request from {{ $consultation->name }}</td>
                                        <td>{{ $consultation->created_at->diffForHumans() }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="3" class="text-center py-4">
                                            {{ __('No recent activity found.') }}
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-layout-admin>
