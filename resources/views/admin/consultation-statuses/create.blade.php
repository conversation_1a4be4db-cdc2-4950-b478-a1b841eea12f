<x-layouts.admin :title="__('Create Consultation Status')">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">{{ __('Create Consultation Status') }}</h1>
            <p class="text-muted small">{{ __('Add a new status for consultation requests') }}</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('admin.settings.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-cog me-2"></i>
                {{ __('Settings') }}
            </a>
            <a href="{{ route('admin.consultation-statuses.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                {{ __('Back to List') }}
            </a>
        </div>
    </div>

    <div class="card border-0 shadow-sm">
        <form action="{{ route('admin.consultation-statuses.store') }}" method="POST">
            @csrf

            <div class="card-body p-4">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="name" class="form-label">{{ __('Name') }} <span class="text-danger">*</span></label>
                            <input type="text" name="name" id="name" value="{{ old('name') }}" class="form-control @error('name') is-invalid @enderror" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="color" class="form-label">{{ __('Color') }} <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-palette"></i>
                                </span>
                                <input type="color" name="color" id="color" value="{{ old('color', '#6c757d') }}" class="form-control form-control-color @error('color') is-invalid @enderror" required>
                            </div>
                            @error('color')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                            <div class="form-text">{{ __('Choose a color for this status') }}</div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="sort_order" class="form-label">{{ __('Sort Order') }} <span class="text-danger">*</span></label>
                            <input type="number" name="sort_order" id="sort_order" value="{{ old('sort_order', 0) }}" min="0" class="form-control @error('sort_order') is-invalid @enderror" required>
                            @error('sort_order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">{{ __('Lower numbers appear first in lists') }}</div>
                        </div>

                        <div class="mb-3 form-check mt-4">
                            <input type="hidden" name="is_default" value="0">
                            <input type="checkbox" name="is_default" id="is_default" value="1" {{ old('is_default') ? 'checked' : '' }} class="form-check-input @error('is_default') is-invalid @enderror">
                            <label class="form-check-label" for="is_default">{{ __('Set as default status') }}</label>
                            @error('is_default')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">{{ __('The default status is automatically assigned to new consultations') }}</div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">{{ __('Description') }}</label>
                    <textarea name="description" id="description" rows="3" class="form-control @error('description') is-invalid @enderror">{{ old('description') }}</textarea>
                    @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mt-4">
                    <div class="alert alert-light border">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <div class="p-2 rounded" style="background-color: {{ old('color', '#6c757d') }}; width: 40px; height: 40px;" id="color-preview"></div>
                            </div>
                            <div>
                                <h5 class="mb-1">{{ __('Preview') }}</h5>
                                <span class="badge" style="background-color: {{ old('color', '#6c757d') }};" id="badge-preview">{{ old('name', 'Status Name') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card-footer bg-white d-flex justify-content-end py-3">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>
                    {{ __('Create Status') }}
                </button>
            </div>
        </form>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const colorInput = document.getElementById('color');
            const nameInput = document.getElementById('name');
            const colorPreview = document.getElementById('color-preview');
            const badgePreview = document.getElementById('badge-preview');

            function updatePreview() {
                const color = colorInput.value;
                const name = nameInput.value || 'Status Name';

                colorPreview.style.backgroundColor = color;
                badgePreview.style.backgroundColor = color;
                badgePreview.textContent = name;
            }

            colorInput.addEventListener('input', updatePreview);
            nameInput.addEventListener('input', updatePreview);
        });
    </script>
</x-layouts.admin>
