<x-layouts.admin :title="__('Manage Consultation Statuses')">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 fw-bold mb-1">{{ __('Consultation Statuses') }}</h1>
            <p class="text-muted mb-0">{{ __('Manage statuses for consultation requests') }}</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('admin.settings.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                {{ __('Back to Settings') }}
            </a>
            <a href="{{ route('admin.consultation-statuses.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                {{ __('New Status') }}
            </a>
        </div>
    </div>

    <div class="card admin-card border-0 shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title fw-bold mb-0">{{ __('All Statuses') }}</h5>
                <span class="badge bg-primary">{{ count($statuses) }} {{ __('Total') }}</span>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th style="width: 50px;">{{ __('Color') }}</th>
                            <th>{{ __('Name') }}</th>
                            <th>{{ __('Description') }}</th>
                            <th>{{ __('Sort Order') }}</th>
                            <th>{{ __('Default') }}</th>
                            <th class="text-end">{{ __('Actions') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse ($statuses as $status)
                            <tr>
                                <td>
                                    <div class="color-swatch" style="background-color: {{ $status->color }}; width: 30px; height: 30px; border-radius: 4px;"></div>
                                </td>
                                <td class="fw-medium">
                                    <span class="badge" style="background-color: {{ $status->color }};">{{ $status->name }}</span>
                                </td>
                                <td>{{ Str::limit($status->description, 50) }}</td>
                                <td>{{ $status->sort_order }}</td>
                                <td>
                                    @if ($status->is_default)
                                        <span class="badge bg-success">{{ __('Default') }}</span>
                                    @else
                                        <form action="{{ route('admin.consultation-statuses.set-default', $status) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('PATCH')
                                            <button type="submit" class="btn btn-sm btn-outline-secondary">
                                                {{ __('Set as Default') }}
                                            </button>
                                        </form>
                                    @endif
                                </td>
                                <td class="text-end">
                                    <div class="btn-group">
                                        <a href="{{ route('admin.consultation-statuses.edit', $status) }}" class="btn btn-sm btn-outline-secondary" data-bs-toggle="tooltip" title="Edit">
                                            <i class="fas fa-pencil-alt"></i>
                                        </a>
                                        @if (!$status->is_default && $status->consultations()->count() === 0)
                                            <form action="{{ route('admin.consultation-statuses.destroy', $status) }}" method="POST" class="d-inline" onsubmit="return confirm('{{ __('Are you sure you want to delete this status?') }}')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-outline-danger" data-bs-toggle="tooltip" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <div class="d-flex flex-column align-items-center py-5">
                                        <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                                        <h5 class="fw-bold">{{ __('No statuses found') }}</h5>
                                        <p class="text-muted">{{ __('Create your first consultation status') }}</p>
                                        <a href="{{ route('admin.consultation-statuses.create') }}" class="btn btn-primary">
                                            <i class="fas fa-plus me-2"></i>
                                            {{ __('New Status') }}
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="mt-4">
        <div class="alert alert-info">
            <div class="d-flex">
                <div class="me-3">
                    <i class="fas fa-info-circle fa-2x"></i>
                </div>
                <div>
                    <h5 class="alert-heading">{{ __('About Consultation Statuses') }}</h5>
                    <p class="mb-0">{{ __('Consultation statuses help you track the progress of consultation requests. The default status is automatically assigned to new consultations. You cannot delete a status that is being used by consultations.') }}</p>

                </div>
            </div>
        </div>
    </div>
</x-layouts.admin>
