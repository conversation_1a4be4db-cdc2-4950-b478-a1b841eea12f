/*
* EndpointSync Website Styles
* Modern, sophisticated design with professional color scheme
*/

:root {
    /* Enhanced Color Palette - Professional Blues with Orange Accent */
    --primary: #ff8c38; /* Changed to stylish orange */
    --primary-light: #ffa563; /* Lighter orange */
    --primary-dark: #e67321; /* Darker orange */
    --primary-soft: rgba(255, 140, 56, 0.08); /* Updated to match new primary */
    --secondary: #f8f9fa;
    --dark: #2d3748;
    --light: #f8f9fa;
    --white: #ffffff;
    --gray: #718096;
    --gray-light: #e2e8f0;
    --success: #48bb78;
    --info: #4299e1;
    --warning: #ecc94b;
    --danger: #e53e3e;
    --gradient-primary: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
    --box-shadow-hover: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* General Styles */
body {
    font-family: 'Poppins', sans-serif;
    color: var(--dark);
    background-color: var(--white);
    line-height: 1.7;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    letter-spacing: -0.02em;
}

.section-title {
    position: relative;
    font-weight: 700;
    margin-bottom: 2.5rem;
    color: var(--dark);
}

.section-title:after {
    content: '';
    position: absolute;
    bottom: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: 3px;
}

.lead {
    font-weight: 400;
    line-height: 1.8;
}

/* Buttons */
.btn {
    padding: 0.625rem 1.75rem;
    font-weight: 500;
    transition: var(--transition);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.btn:hover::after {
    transform: translateY(0);
}

.btn-lg {
    padding: 0.875rem 2.25rem;
    font-size: 1.1rem;
}

.btn-primary {
    background: var(--gradient-primary);
    border: none;
    color: var(--white);
    font-weight: 600;
    letter-spacing: 0.5px;
    text-transform: capitalize;
}

.btn-primary:hover, .btn-primary:focus {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    color: var(--white);
}

.btn-outline-primary {
    color: var(--primary);
    border: 2px solid var(--primary);
    background: transparent;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-transform: capitalize;
}

.btn-outline-primary:hover, .btn-outline-primary:focus {
    background-color: var(--primary);
    border-color: var(--primary);
    transform: translateY(-2px);
    color: var(--white);
}

.rounded-pill {
    border-radius: 50px !important;
}

/* Navbar */
.navbar {
    padding: 1rem 0;
    transition: var(--transition);
    background-color: rgba(255, 255, 255, 0.98);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
}

.navbar-scrolled {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.navbar-brand-text {
    font-size: 1.6rem;
    font-weight: 700;
    letter-spacing: -0.03em;
}

.logo-endpoint {
    color: var(--primary);
    font-weight: 700;
}

.logo-endpoint.text-white {
    color: var(--white) !important;
}

.logo-sync {
    color: var(--dark);
    font-weight: 600;
}

.logo-sync.text-white {
    color: var(--white) !important;
}

.navbar-light .navbar-nav .nav-link {
    color: var(--dark);
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: var(--transition);
    position: relative;
}

.navbar-light .navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-light .navbar-nav .nav-link:hover::after,
.navbar-light .navbar-nav .nav-link:focus::after {
    width: 30px;
}

.navbar-light .navbar-nav .nav-link:hover,
.navbar-light .navbar-nav .nav-link:focus {
    color: var(--primary);
}

/* Hero Section */
.hero-section {
    position: relative;
    background-color: var(--white);
    background-image: url('../assets/img/sections/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    padding: 6rem 0 4rem; /* Reduced padding from 8rem 0 6rem */
    min-height: 80vh; /* Reduced from 100vh to 80vh */
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(44, 82, 130, 0.92) 0%, rgba(74, 105, 189, 0.92) 100%);
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 80%); /* Changed from 85% to 80% to reduce height */
}

.hero-section h1 {
    font-size: 3.2rem; /* Reduced from 3.5rem */
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem; /* Reduced from 1.5rem */
    color: var(--white);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    letter-spacing: -0.03em;
}

.hero-section h2 {
    font-weight: 500;
    margin-bottom: 1rem; /* Reduced from 1.5rem */
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.hero-section .lead {
    font-size: 1.1rem; /* Reduced from 1.2rem */
    margin-bottom: 1.5rem; /* Reduced from 2.5rem */
    color: rgba(255, 255, 255, 0.85);
}

.hero-section .img-fluid {
    border-radius: 12px;
    box-shadow: var(--box-shadow-hover);
    transition: var(--transition);
    max-height: 350px; /* Added max-height to control image size */
    object-fit: cover; /* Ensure image maintains aspect ratio */
}

/* Parallax Sections */
.parallax-section {
    position: relative;
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    z-index: 1;
    transition: background-position 0.3s ease;
}

.process-section {
    background-image: url('../assets/img/sections/process-bg.jpg');
    color: var(--white);
    position: relative;
}

.process-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(26, 54, 93, 0.92) 0%, rgba(44, 82, 130, 0.92) 100%);
    z-index: -1;
    backdrop-filter: blur(2px);
}

.process-section .section-title {
    color: rgba(255, 255, 255, 0.95); /* Lighter color for better readability */
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* Add shadow for better contrast */
    font-weight: 600; /* Slightly lighter font weight */
    position: relative;
    z-index: 2;
}

.process-section .section-title:after {
    background: rgba(255, 255, 255, 0.7); /* Lighter underline */
    height: 4px; /* Slightly thicker */
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.5); /* Add glow effect */
}

/* Additional styling for process title */
.process-title {
    letter-spacing: 0.5px; /* Increase letter spacing for better readability */
    color: white !important; /* Force white color */
}

.services-section {
    background-image: url('../assets/img/sections/services-bg.jpg');
    position: relative;
}

.services-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    z-index: -1;
    backdrop-filter: blur(2px);
}

.contact-section {
    background-image: url('../assets/img/sections/contact-bg.jpg');
    position: relative;
}

.contact-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(44, 82, 130, 0.92) 0%, rgba(26, 54, 93, 0.92) 100%);
    z-index: -1;
    backdrop-filter: blur(2px);
}

.contact-section .section-title {
    color: rgba(255, 255, 255, 0.95); /* Lighter color for better readability */
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* Add shadow for better contrast */
    font-weight: 600; /* Slightly lighter font weight */
    position: relative;
    z-index: 2;
}

.contact-section .section-title:after {
    background: rgba(255, 255, 255, 0.7); /* Lighter underline */
    height: 4px; /* Slightly thicker */
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.5); /* Add glow effect */
}

/* Additional styling for contact title */
.contact-title {
    letter-spacing: 0.5px; /* Increase letter spacing for better readability */
    color: white !important; /* Force white color */
}

.contact-section .lead {
    color: rgba(255, 255, 255, 0.9); /* Lighter text for better readability */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); /* Subtle shadow for better contrast */
    font-weight: 400;
    max-width: 800px;
    margin: 0 auto 2rem;
}

/* Process Cards */
.process-card {
    position: relative;
    z-index: 1;
    padding: 2.5rem 1.5rem;
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition);
    height: 100%;
}

.process-card:hover {
    transform: translateY(-8px);
    background-color: rgba(255, 255, 255, 0.12);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.process-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    position: relative;
    z-index: 1;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.process-icon::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    z-index: -1;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.8;
    }
    70% {
        transform: scale(1.1);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

.process-number {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--white);
}

.process-card h3 {
    margin-bottom: 1rem;
    font-weight: 600;
}

.process-card p {
    color: rgba(255, 255, 255, 0.85);
    font-size: 0.95rem;
}

/* Benefit Cards */
.benefit-card {
    background-color: var(--white);
    border-radius: 12px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    height: 100%;
    padding: 2.5rem 1.5rem;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.benefit-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--gradient-primary);
    z-index: -1;
    transition: var(--transition);
}

.benefit-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--box-shadow-hover);
}

.benefit-card:hover::before {
    height: 7px;
}

.benefit-icon {
    color: var(--primary);
    margin-bottom: 1.5rem;
    position: relative;
}

.benefit-icon::after {
    content: '';
    position: absolute;
    width: 50px;
    height: 50px;
    background-color: var(--primary-soft);
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: -1;
}

.benefit-card h3 {
    margin-bottom: 1rem;
    font-weight: 600;
    font-size: 1.3rem;
}

.benefit-card p {
    color: var(--gray);
    font-size: 0.95rem;
}

/* Service Cards */
.card {
    transition: var(--transition);
    border-radius: 12px;
    border: none;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: var(--box-shadow-hover) !important;
}

.card-img-top {
    height: 240px;
    object-fit: cover;
    object-position: center;
}

.card-body {
    padding: 2rem;
}

.card-title {
    font-weight: 600;
    margin-bottom: 1rem;
}

.card-text {
    color: var(--gray);
    margin-bottom: 1.5rem;
    flex-grow: 1;
}

.bg-primary-soft {
    background-color: var(--primary-soft);
    color: var(--primary);
    font-weight: 500;
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
    border-radius: 6px;
}

.service-tags {
    display: flex;
    flex-wrap: wrap;
    margin-top: auto;
}

/* FAQ Accordion */
.accordion-item {
    border-radius: 12px !important;
    overflow: hidden;
    transition: var(--transition);
}

.accordion-item:hover {
    box-shadow: var(--box-shadow);
}

.accordion-button {
    padding: 1.25rem 1.5rem;
    font-weight: 500;
    font-size: 1.05rem;
    color: var(--dark);
    background-color: var(--white);
}

.accordion-button:not(.collapsed) {
    background-color: var(--primary-soft);
    color: var(--primary);
    box-shadow: none;
    font-weight: 600;
}

.accordion-button:focus {
    box-shadow: none;
    border-color: var(--primary-light);
}

.accordion-button::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%232c5282'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
    transition: var(--transition);
}

.accordion-body {
    padding: 1.25rem 1.5rem;
    color: var(--gray);
}

/* Contact Form */
.form-control, .form-select {
    padding: 0.875rem 1.25rem;
    border-radius: 8px;
    border: 1px solid var(--gray-light);
    transition: var(--transition);
    font-size: 1rem;
    background-color: rgba(255, 255, 255, 0.9);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-light);
    box-shadow: 0 0 0 0.25rem rgba(255, 140, 56, 0.15);
    background-color: var(--white);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--dark);
}

/* Consultation Form Styling */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
}

.consultation-form .form-control,
.consultation-form .form-select,
.consultation-form .input-group-text {
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.consultation-form .form-control-lg {
    padding: 0.75rem 1.25rem;
}

.consultation-form .form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.25rem rgba(255, 140, 56, 0.15);
    transform: translateY(-2px);
}

.consultation-form .input-group-text {
    color: var(--gray);
}

.consultation-form label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.consultation-form .form-text {
    font-size: 0.85rem;
    color: var(--gray);
}

.consultation-form textarea {
    min-height: 100px;
    transition: height 0.3s ease;
}

.consultation-form textarea:focus {
    height: 120px;
}

.consultation-form .btn-primary {
    background: var(--gradient-primary);
    border: none;
    box-shadow: 0 4px 15px rgba(255, 140, 56, 0.3);
    transition: all 0.3s ease;
}

.consultation-form .btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(255, 140, 56, 0.4);
}

.consultation-form .btn-primary:active {
    transform: translateY(0);
}

/* Success message animation */
.success-animation i {
    animation-duration: 1s;
}

/* Valid input styling */
.was-validated .form-control:valid {
    border-color: var(--success);
    background-image: none;
}

.was-validated .form-control:valid + .input-group-text .fa-check-circle {
    display: inline-block !important;
}

.was-validated .form-control:valid:focus {
    box-shadow: 0 0 0 0.25rem rgba(72, 187, 120, 0.15);
}

/* About Section */
.about-section img {
    border-radius: 12px;
    box-shadow: var(--box-shadow-hover);
}

/* Footer */
footer {
    background-color: var(--dark);
    color: var(--white);
    position: relative;
    overflow: hidden;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(74, 105, 189, 0.1) 0%, rgba(44, 82, 130, 0) 70%);
    border-radius: 50%;
}

footer a {
    transition: var(--transition);
    position: relative;
}

footer a:hover {
    color: var(--primary-light) !important;
    text-decoration: none;
}

footer a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 1px;
    background-color: var(--primary-light);
    transition: var(--transition);
}

footer a:hover::after {
    width: 100%;
}

.social-icons a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    transition: var(--transition);
    margin-right: 0.75rem;
}

.social-icons a:hover {
    background-color: var(--primary);
    transform: translateY(-3px);
}

.social-icons a:hover::after {
    width: 0; /* Override the underline effect for social icons */
}

footer h5 {
    font-weight: 600;
    margin-bottom: 1.5rem;
    position: relative;
    display: inline-block;
}

footer h5::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: var(--primary-light);
}

/* AOS Animation Enhancements */
[data-aos] {
    pointer-events: none;
}

[data-aos].aos-animate {
    pointer-events: auto;
}

[data-aos="zoom-in-up"] {
    transform: scale(0.9) translateY(30px);
    opacity: 0;
    transition-property: transform, opacity;
}

[data-aos="zoom-in-up"].aos-animate {
    transform: scale(1) translateY(0);
    opacity: 1;
}

/* Image Enhancements */
.img-fluid.rounded-3.shadow-lg {
    transition: transform 0.5s ease, box-shadow 0.5s ease;
}

.img-fluid.rounded-3.shadow-lg:hover {
    transform: scale(1.02);
    box-shadow: 0 20px 30px rgba(0, 0, 0, 0.15) !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fadeInUp {
    animation: fadeInUp 0.5s ease forwards;
}

/* Responsive Adjustments */
@media (max-width: 1199.98px) {
    .hero-section h1 {
        font-size: 3rem;
    }
}

@media (max-width: 991.98px) {
    .navbar {
        padding: 0.75rem 0;
    }

    .hero-section {
        padding: 5rem 0 3rem; /* Reduced from 7rem 0 5rem */
        text-align: center;
        min-height: auto; /* Remove fixed height on mobile */
    }

    .hero-section h1 {
        font-size: 2.5rem;
    }

    .hero-section .img-fluid {
        margin-top: 2rem; /* Reduced from 3rem */
    }

    .process-card, .benefit-card {
        margin-bottom: 1rem;
    }
}

@media (max-width: 767.98px) {
    .section-title {
        font-size: 1.75rem;
    }

    .hero-section h1 {
        font-size: 2.25rem;
    }

    .hero-section h2 {
        font-size: 1.25rem;
    }

    .process-card, .benefit-card {
        padding: 2rem 1.25rem;
    }

    .card-body {
        padding: 1.5rem;
    }
}

@media (max-width: 575.98px) {
    .hero-section h1 {
        font-size: 2rem;
    }

    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
}

/* Legal Pages */
.legal-content {
    padding-top: 120px; /* Adjust based on navbar height */
    padding-bottom: 60px;
}

.legal-content h1 {
    margin-bottom: 30px;
}

.legal-content h2 {
    margin-top: 40px;
    margin-bottom: 20px;
    font-size: 1.75rem;
}

.legal-content p {
    margin-bottom: 15px;
    line-height: 1.7;
}

.legal-content ul {
    margin-bottom: 15px;
    padding-left: 20px;
}

/* Authentication Pages */
.auth-container {
    position: relative;
    min-height: 100vh;
    background-color: var(--white);
    background-image: url('../assets/img/sections/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    overflow: hidden;
}

.auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(44, 82, 130, 0.92) 0%, rgba(74, 105, 189, 0.92) 100%);
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 80%);
    z-index: 0;
}

.auth-card {
    position: relative;
    z-index: 1;
    background-color: var(--white);
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: var(--transition);
}

.auth-card-body {
    padding: 2.5rem;
}

.auth-card h1, .auth-card h2, .auth-card h3 {
    color: var(--dark);
    margin-bottom: 1rem;
}

.auth-card .form-control {
    border-radius: 8px;
    padding: 0.75rem 1rem;
    border: 1px solid var(--gray-light);
    transition: var(--transition);
}

.auth-card .form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.25rem rgba(255, 140, 56, 0.15);
}

.auth-card .btn-primary {
    background: var(--gradient-primary);
    border: none;
    color: var(--white);
    font-weight: 600;
    letter-spacing: 0.5px;
    text-transform: capitalize;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(255, 140, 56, 0.3);
    transition: all 0.3s ease;
    width: 100%;
}

.auth-card .btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(255, 140, 56, 0.4);
}

.auth-card .btn-primary:active {
    transform: translateY(0);
}

.auth-card a {
    color: var(--primary);
    transition: var(--transition);
}

.auth-card a:hover {
    color: var(--primary-dark);
    text-decoration: none;
}
